/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    403: typeof import('./components/ErrorMessage/403.vue')['default']
    404: typeof import('./components/ErrorMessage/404.vue')['default']
    500: typeof import('./components/ErrorMessage/500.vue')['default']
    BLCol: typeof import('./components/Common/BLCol.vue')['default']
    BLRow: typeof import('./components/Common/BLRow.vue')['default']
    CButton: typeof import('./components/Tools/c-button.vue')['default']
    CCard: typeof import('./components/Tools/c-card.vue')['default']
    CheckCard: typeof import('./components/CheckCard/index.vue')['default']
    ChooseModule: typeof import('./components/ChooseModule/index.vue')['default']
    CInputText: typeof import('./components/Tools/c-input-text.vue')['default']
    Closer: typeof import('./components/Closer/index.vue')['default']
    CodeHighLight: typeof import('./components/CodeHighLight/index.vue')['default']
    ColSetting: typeof import('./components/ProTable/components/ColSetting.vue')['default']
    CropUpload: typeof import('./components/CropUpload/index.vue')['default']
    CTooltip: typeof import('./components/Tools/c-tooltip.vue')['default']
    CustomFileSelector: typeof import('./components/CustomFileSelector/index.vue')['default']
    DatePicker: typeof import('./components/Form/DatePicker/index.vue')['default']
    ECharts: typeof import('./components/ECharts/index.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ESign: typeof import('./components/ESign/index.vue')['default']
    FormatTransformer: typeof import('./components/Tools/FormatTransformer.vue')['default']
    FormContainer: typeof import('./components/Form/FormContainer/index.vue')['default']
    Grid: typeof import('./components/Grid/index.vue')['default']
    GridItem: typeof import('./components/Grid/components/GridItem.vue')['default']
    Icon: typeof import('./components/Icon/index.vue')['default']
    InputCopyable: typeof import('./components/Tools/InputCopyable.vue')['default']
    LanguageSwitch: typeof import('./components/LanguageSwitch.vue')['default']
    Loading: typeof import('./components/Loading/index.vue')['default']
    MenuSelector: typeof import('./components/Selectors/MenuSelector/index.vue')['default']
    OrgSelector: typeof import('./components/Selectors/OrgSelector/index.vue')['default']
    Pagination: typeof import('./components/ProTable/components/Pagination.vue')['default']
    PositionSelector: typeof import('./components/Selectors/PositionSelector/index.vue')['default']
    ProTable: typeof import('./components/ProTable/index.vue')['default']
    RoleSelector: typeof import('./components/Selectors/RoleSelector/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SButton: typeof import('./components/Form/SButton/index.vue')['default']
    SCheckboxGroup: typeof import('./components/Form/SCheckboxGroup/index.vue')['default']
    SearchForm: typeof import('./components/SearchForm/index.vue')['default']
    SearchFormItem: typeof import('./components/SearchForm/components/SearchFormItem.vue')['default']
    SelectFilter: typeof import('./components/SelectFilter/index.vue')['default']
    SelectIcon: typeof import('./components/SelectIcon/index.vue')['default']
    SelectIconPlus: typeof import('./components/SelectIconPlus/index.vue')['default']
    SFormItem: typeof import('./components/Form/SFormItem/index.vue')['default']
    SInput: typeof import('./components/Form/SInput/index.vue')['default']
    SkeletonLoading: typeof import('./components/SkeletonLoading.vue')['default']
    SRadioGroup: typeof import('./components/Form/SRadioGroup/index.vue')['default']
    SSelect: typeof import('./components/Form/SSelect/index.vue')['default']
    SvgIcon: typeof import('./components/SvgIcon/index.vue')['default']
    SwitchDark: typeof import('./components/SwitchDark/index.vue')['default']
    TableColumn: typeof import('./components/ProTable/components/TableColumn.vue')['default']
    TextareaCopyable: typeof import('./components/Tools/TextareaCopyable.vue')['default']
    TreeFilter: typeof import('./components/TreeFilter/index.vue')['default']
    UploadImg: typeof import('./components/Upload/UploadImg.vue')['default']
    UploadImgs: typeof import('./components/Upload/UploadImgs.vue')['default']
    UserSelector: typeof import('./components/Selectors/UserSelector/index.vue')['default']
  }
}
