/**
 * 语言同步服务
 * 处理前端语言设置与后端的同步
 * <AUTHOR>
 */

import { logger } from "ee-core/log";
import { setLanguage, getCurrentLanguage, SupportedLanguage, initI18n } from "../../data/i18n/i18n";
import { ApiResponse } from "../../data/debug/apiResponse";
import { ERROR_CODES, getErrorMessage } from "../../data/debug/errorCodes";

/**
 * 语言同步服务类
 */
class LanguageService {
  constructor() {
    // 初始化国际化系统
    initI18n();
    logger.info("[LanguageService] Language service initialized");
  }

  /**
   * 设置语言
   * @param language 语言代码
   * @returns API响应
   */
  async setLanguage(language: string): Promise<ApiResponse> {
    try {
      logger.info(`[LanguageService] Setting language to: ${language}`);
      
      // 验证语言代码
      const supportedLanguages: SupportedLanguage[] = ['zh', 'en', 'es', 'fr'];
      if (supportedLanguages.indexOf(language as SupportedLanguage) === -1) {
        logger.warn(`[LanguageService] Unsupported language: ${language}, fallback to zh`);
        language = 'zh';
      }

      // 设置语言
      setLanguage(language as SupportedLanguage);
      
      logger.info(`[LanguageService] Language set successfully to: ${language}`);
      return new ApiResponse(ERROR_CODES.SUCCESS, getErrorMessage("SUCCESS"), { language });
    } catch (error) {
      logger.error("[LanguageService] Error setting language:", error);
      return new ApiResponse(ERROR_CODES.INTERNAL_ERROR, getErrorMessage("INTERNAL_ERROR"));
    }
  }

  /**
   * 获取当前语言
   * @returns API响应，包含当前语言
   */
  async getCurrentLanguage(): Promise<ApiResponse> {
    try {
      const currentLang = getCurrentLanguage();
      logger.info(`[LanguageService] Current language: ${currentLang}`);
      return new ApiResponse(ERROR_CODES.SUCCESS, getErrorMessage("SUCCESS"), { language: currentLang });
    } catch (error) {
      logger.error("[LanguageService] Error getting current language:", error);
      return new ApiResponse(ERROR_CODES.INTERNAL_ERROR, getErrorMessage("INTERNAL_ERROR"));
    }
  }

  /**
   * 同步前端语言设置
   * @param req 包含语言设置的请求
   * @returns API响应
   */
  async syncLanguageFromFrontend(req: { language: string }): Promise<ApiResponse> {
    const startTime = Date.now();
    try {
      const { language } = req;
      logger.info(
        `[LanguageService] Syncing language from frontend: ${language}`
      );

      const result = await this.setLanguage(language);
      const duration = Date.now() - startTime;
      logger.info(`[LanguageService] Language sync completed in ${duration}ms`);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(
        `[LanguageService] Error syncing language from frontend (${duration}ms):`,
        error
      );
      return new ApiResponse(
        ERROR_CODES.INTERNAL_ERROR,
        getErrorMessage("INTERNAL_ERROR")
      );
    }
  }
}

// 创建单例实例
const languageService = new LanguageService();

export { LanguageService, languageService };
