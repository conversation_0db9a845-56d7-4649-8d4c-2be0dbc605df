import { BrowserWindow, app, powerMonitor } from 'electron';
import { getMainWindow } from 'ee-core/electron';
import { logger } from 'ee-core/log';
import fs from 'fs';
import path from 'path';

interface WindowState {
  x?: number;
  y?: number;
  width: number;
  height: number;
  isMaximized: boolean;
  isFullScreen: boolean;
  isMinimized: boolean;
  lastRoute?: string;
  timestamp: number;
}

interface AppState {
  windowState: WindowState;
  userPreferences: Record<string, any>;
  appStatus: "running" | "suspended" | "exited";
  sessionId: string;
}

/**
 * 窗口状态管理服务
 * 负责保存和恢复窗口状态，处理系统休眠/唤醒事件
 */
class WindowStateService {
  private stateFilePath: string;
  private saveTimer: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private lastSavedState: WindowState | null = null;
  private currentSessionId: string;
  private appStatus: "running" | "suspended" | "exited" = "running";

  constructor() {
    // 获取用户数据目录
    const userDataPath = app.getPath("userData");
    this.stateFilePath = path.join(userDataPath, "window-state.json");

    // 生成当前会话ID
    this.currentSessionId =
      Date.now().toString() + "-" + Math.random().toString(36).substr(2, 9);

    // 确保目录存在
    const dir = path.dirname(this.stateFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  /**
   * 初始化窗口状态管理
   */
  public initialize(): void {
    if (this.isInitialized) return;

    logger.info("[WindowStateService] 初始化窗口状态管理");

    // 标记应用为运行状态
    this.setAppStatus("running");

    // 监听系统电源事件
    this.setupPowerMonitoring();

    // 监听应用事件
    this.setupAppEvents();

    this.isInitialized = true;
  }

  /**
   * 设置应用状态
   */
  public setAppStatus(status: "running" | "suspended" | "exited"): void {
    this.appStatus = status;
    logger.info(`[WindowStateService] 应用状态变更为: ${status}`);
  }

  /**
   * 获取应用状态
   */
  public getAppStatus(): "running" | "suspended" | "exited" {
    return this.appStatus;
  }

  /**
   * 设置电源监控
   */
  private setupPowerMonitoring(): void {
    try {
      // 监听系统休眠事件
      powerMonitor.on("suspend", () => {
        logger.info("[WindowStateService] 系统即将休眠，保存窗口状态");
        this.setAppStatus("suspended");
        this.saveWindowState();
      });

      // 监听系统唤醒事件
      powerMonitor.on("resume", () => {
        logger.info("[WindowStateService] 系统已唤醒，准备恢复窗口状态");
        this.setAppStatus("running");
        // 延迟一点时间确保系统完全唤醒
        setTimeout(() => {
          this.restoreWindowState();
        }, 1000);
      });

      // 监听锁屏事件
      powerMonitor.on("lock-screen", () => {
        logger.info("[WindowStateService] 屏幕已锁定，保存窗口状态");
        this.saveWindowState();
      });

      // 监听解锁事件
      powerMonitor.on("unlock-screen", () => {
        logger.info("[WindowStateService] 屏幕已解锁");
        setTimeout(() => {
          this.restoreWindowState();
        }, 500);
      });
    } catch (error) {
      logger.warn("[WindowStateService] 电源监控设置失败:", error);
    }
  }

  /**
   * 设置应用事件监听
   */
  private setupAppEvents(): void {
    // 应用即将退出时标记退出状态并清除状态文件
    app.on("before-quit", () => {
      logger.info("[WindowStateService] 应用即将退出，清除状态文件");
      this.setAppStatus("exited");
      this.clearStateFile();
    });

    // 窗口关闭时标记退出状态并清除状态文件
    app.on("window-all-closed", () => {
      logger.info("[WindowStateService] 所有窗口已关闭，清除状态文件");
      this.setAppStatus("exited");
      this.clearStateFile();
    });
  }

  /**
   * 获取当前窗口状态
   */
  private getCurrentWindowState(): WindowState | null {
    try {
      const mainWindow = getMainWindow();
      if (!mainWindow || mainWindow.isDestroyed()) {
        return null;
      }

      const bounds = mainWindow.getBounds();
      const state: WindowState = {
        x: bounds.x,
        y: bounds.y,
        width: bounds.width,
        height: bounds.height,
        isMaximized: mainWindow.isMaximized(),
        isFullScreen: mainWindow.isFullScreen(),
        isMinimized: mainWindow.isMinimized(),
        timestamp: Date.now(),
      };

      return state;
    } catch (error) {
      logger.error("[WindowStateService] 获取窗口状态失败:", error);
      return null;
    }
  }

  /**
   * 保存窗口状态到文件
   */
  public saveWindowState(): void {
    try {
      const currentState = this.getCurrentWindowState();
      if (!currentState) {
        logger.warn("[WindowStateService] 无法获取当前窗口状态");
        return;
      }

      // 避免频繁保存相同状态
      if (
        this.lastSavedState &&
        this.isStateEqual(currentState, this.lastSavedState)
      ) {
        return;
      }

      const appState: AppState = {
        windowState: currentState,
        userPreferences: this.getUserPreferences(),
        appStatus: this.appStatus,
        sessionId: this.currentSessionId,
      };

      fs.writeFileSync(
        this.stateFilePath,
        JSON.stringify(appState, null, 2),
        "utf8"
      );
      this.lastSavedState = currentState;

      logger.info("[WindowStateService] 窗口状态已保存:", {
        x: currentState.x,
        y: currentState.y,
        width: currentState.width,
        height: currentState.height,
        isMaximized: currentState.isMaximized,
      });
    } catch (error) {
      logger.error("[WindowStateService] 保存窗口状态失败:", error);
    }
  }

  /**
   * 从文件加载窗口状态
   */
  public loadWindowState(): WindowState | null {
    try {
      if (!fs.existsSync(this.stateFilePath)) {
        logger.info("[WindowStateService] 窗口状态文件不存在，使用默认状态");
        return null;
      }

      const data = fs.readFileSync(this.stateFilePath, "utf8");
      const appState: AppState = JSON.parse(data);

      // 检查应用状态，如果是退出状态则不恢复
      if (appState.appStatus === "exited") {
        logger.info("[WindowStateService] 应用已正常退出，不恢复窗口状态");
        this.clearStateFile();
        return null;
      }

      // 检查状态是否过期（超过7天）
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
      if (Date.now() - appState.windowState.timestamp > maxAge) {
        logger.info("[WindowStateService] 窗口状态已过期，使用默认状态");
        this.clearStateFile();
        return null;
      }

      // 只有在suspended状态下才恢复（表示是休眠/锁屏后恢复）
      if (appState.appStatus === "suspended") {
        logger.info(
          "[WindowStateService] 检测到休眠状态，准备恢复窗口状态:",
          appState.windowState
        );
        return appState.windowState;
      } else {
        logger.info(
          "[WindowStateService] 应用状态不是suspended，不恢复窗口状态"
        );
        return null;
      }
    } catch (error) {
      logger.error("[WindowStateService] 加载窗口状态失败:", error);
      return null;
    }
  }

  /**
   * 恢复窗口状态
   */
  public restoreWindowState(): void {
    try {
      const mainWindow = getMainWindow();
      if (!mainWindow || mainWindow.isDestroyed()) {
        logger.warn("[WindowStateService] 主窗口不存在，无法恢复状态");
        return;
      }

      const savedState = this.loadWindowState();
      if (!savedState) {
        return;
      }

      // 恢复窗口位置和大小
      if (savedState.x !== undefined && savedState.y !== undefined) {
        mainWindow.setBounds({
          x: savedState.x,
          y: savedState.y,
          width: savedState.width,
          height: savedState.height,
        });
      } else {
        mainWindow.setSize(savedState.width, savedState.height);
        mainWindow.center();
      }

      // 恢复窗口状态
      if (savedState.isMaximized) {
        mainWindow.maximize();
      } else if (savedState.isFullScreen) {
        mainWindow.setFullScreen(true);
      }

      // 确保窗口可见
      if (!savedState.isMinimized) {
        mainWindow.show();
        mainWindow.focus();
      }

      logger.info("[WindowStateService] 窗口状态已恢复");
    } catch (error) {
      logger.error("[WindowStateService] 恢复窗口状态失败:", error);
    }
  }

  /**
   * 定时保存窗口状态
   */
  public startPeriodicSave(interval: number = 30000): void {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
    }

    this.saveTimer = setInterval(() => {
      this.saveWindowState();
    }, interval);

    logger.info(
      `[WindowStateService] 开始定时保存窗口状态，间隔: ${interval}ms`
    );
  }

  /**
   * 停止定时保存
   */
  public stopPeriodicSave(): void {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
      this.saveTimer = null;
      logger.info("[WindowStateService] 停止定时保存窗口状态");
    }
  }

  /**
   * 获取用户偏好设置
   */
  private getUserPreferences(): Record<string, any> {
    // 这里可以获取一些用户偏好设置
    return {
      theme: "default",
      language: "zh",
    };
  }

  /**
   * 比较两个状态是否相等
   */
  private isStateEqual(state1: WindowState, state2: WindowState): boolean {
    return (
      state1.x === state2.x &&
      state1.y === state2.y &&
      state1.width === state2.width &&
      state1.height === state2.height &&
      state1.isMaximized === state2.isMaximized &&
      state1.isFullScreen === state2.isFullScreen &&
      state1.isMinimized === state2.isMinimized
    );
  }

  /**
   * 清除状态文件
   */
  public clearStateFile(): void {
    try {
      if (fs.existsSync(this.stateFilePath)) {
        fs.unlinkSync(this.stateFilePath);
        logger.info("[WindowStateService] 状态文件已清除");
      }
    } catch (error) {
      logger.warn("[WindowStateService] 清除状态文件失败:", error);
    }
  }

  /**
   * 清理过期的状态文件
   */
  public cleanup(): void {
    try {
      if (fs.existsSync(this.stateFilePath)) {
        const data = fs.readFileSync(this.stateFilePath, "utf8");
        const appState: AppState = JSON.parse(data);

        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
        if (Date.now() - appState.windowState.timestamp > maxAge) {
          fs.unlinkSync(this.stateFilePath);
          logger.info("[WindowStateService] 已清理过期的状态文件");
        }
      }
    } catch (error) {
      logger.warn("[WindowStateService] 清理状态文件失败:", error);
    }
  }
}

// 创建单例实例
const windowStateService = new WindowStateService();

export { windowStateService, WindowStateService };
