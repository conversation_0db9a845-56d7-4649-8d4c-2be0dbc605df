/**
 * 授权验证性能监控工具
 */

interface AuthPerformanceMetrics {
  checkAuthStart: number;
  checkAuthEnd: number;
  languageSyncStart: number;
  languageSyncEnd: number;
  routeLoadStart: number;
  routeLoadEnd: number;
  deviceControllerInitStart: number;
  deviceControllerInitEnd: number;
}

class AuthPerformanceMonitor {
  private metrics: Partial<AuthPerformanceMetrics> = {};
  private startTime: number;

  constructor() {
    this.startTime = performance.now();
    console.log("🚀 [AuthPerformance] 授权性能监控开始");
  }

  // 标记授权检查开始
  markCheckAuthStart() {
    this.metrics.checkAuthStart = performance.now();
    this.logMetric("授权检查开始", this.metrics.checkAuthStart);
  }

  // 标记授权检查结束
  markCheckAuthEnd() {
    this.metrics.checkAuthEnd = performance.now();
    this.logMetric("授权检查结束", this.metrics.checkAuthEnd);

    if (this.metrics.checkAuthStart) {
      const duration = this.metrics.checkAuthEnd - this.metrics.checkAuthStart;
      console.log(`🔐 [AuthPerformance] 授权检查耗时: ${duration.toFixed(2)}ms`);
    }
  }

  // 标记语言同步开始
  markLanguageSyncStart() {
    this.metrics.languageSyncStart = performance.now();
    this.logMetric("语言同步开始", this.metrics.languageSyncStart);
  }

  // 标记语言同步结束
  markLanguageSyncEnd() {
    this.metrics.languageSyncEnd = performance.now();
    this.logMetric("语言同步结束", this.metrics.languageSyncEnd);

    if (this.metrics.languageSyncStart) {
      const duration = this.metrics.languageSyncEnd - this.metrics.languageSyncStart;
      console.log(`🌐 [AuthPerformance] 语言同步耗时: ${duration.toFixed(2)}ms`);
    }
  }

  // 标记路由加载开始
  markRouteLoadStart() {
    this.metrics.routeLoadStart = performance.now();
    this.logMetric("路由加载开始", this.metrics.routeLoadStart);
  }

  // 标记路由加载结束
  markRouteLoadEnd() {
    this.metrics.routeLoadEnd = performance.now();
    this.logMetric("路由加载结束", this.metrics.routeLoadEnd);

    if (this.metrics.routeLoadStart) {
      const duration = this.metrics.routeLoadEnd - this.metrics.routeLoadStart;
      console.log(`🛣️ [AuthPerformance] 路由加载耗时: ${duration.toFixed(2)}ms`);
    }
  }

  // 标记设备控制器初始化开始
  markDeviceControllerInitStart() {
    this.metrics.deviceControllerInitStart = performance.now();
    this.logMetric("设备控制器初始化开始", this.metrics.deviceControllerInitStart);
  }

  // 标记设备控制器初始化结束
  markDeviceControllerInitEnd() {
    this.metrics.deviceControllerInitEnd = performance.now();
    this.logMetric("设备控制器初始化结束", this.metrics.deviceControllerInitEnd);

    if (this.metrics.deviceControllerInitStart) {
      const duration = this.metrics.deviceControllerInitEnd - this.metrics.deviceControllerInitStart;
      console.log(`🔧 [AuthPerformance] 设备控制器初始化耗时: ${duration.toFixed(2)}ms`);
    }
  }

  private logMetric(name: string, time: number) {
    const duration = time - this.startTime;
    console.log(`📊 [AuthPerformance] ${name}: ${duration.toFixed(2)}ms`);
  }

  // 生成完整的性能报告
  generateReport() {
    console.group("📊 授权启动性能报告");

    const report = {
      授权检查耗时: this.getTimeDiff("checkAuthStart", "checkAuthEnd"),
      语言同步耗时: this.getTimeDiff("languageSyncStart", "languageSyncEnd"),
      路由加载耗时: this.getTimeDiff("routeLoadStart", "routeLoadEnd"),
      设备控制器初始化耗时: this.getTimeDiff("deviceControllerInitStart", "deviceControllerInitEnd"),
      总启动时间: this.getTotalTime()
    };

    Object.entries(report).forEach(([key, value]) => {
      if (value !== null) {
        console.log(`${key}: ${value}ms`);
      }
    });

    // 性能建议
    this.generateSuggestions();

    console.groupEnd();
  }

  private getTimeDiff(startMetric: keyof AuthPerformanceMetrics, endMetric: keyof AuthPerformanceMetrics): number | null {
    const startTime = this.metrics[startMetric];
    const endTime = this.metrics[endMetric];
    return startTime && endTime ? Number((endTime - startTime).toFixed(2)) : null;
  }

  private getTotalTime(): number | null {
    const endTime = this.metrics.deviceControllerInitEnd || this.metrics.routeLoadEnd || this.metrics.checkAuthEnd;
    return endTime ? Number((endTime - this.startTime).toFixed(2)) : null;
  }

  private generateSuggestions() {
    console.group("💡 性能优化建议");

    const authDuration = this.getTimeDiff("checkAuthStart", "checkAuthEnd");
    if (authDuration && authDuration > 5000) {
      console.warn("⚠️ 授权检查耗时过长，建议添加缓存机制");
    }

    const languageDuration = this.getTimeDiff("languageSyncStart", "languageSyncEnd");
    if (languageDuration && languageDuration > 1000) {
      console.warn("⚠️ 语言同步耗时过长，建议异步处理");
    }

    const routeDuration = this.getTimeDiff("routeLoadStart", "routeLoadEnd");
    if (routeDuration && routeDuration > 2000) {
      console.warn("⚠️ 路由加载耗时过长，建议优化动态路由加载");
    }

    const totalTime = this.getTotalTime();
    if (totalTime && totalTime > 10000) {
      console.warn("⚠️ 总启动时间过长，建议进行全面优化");
    }

    console.groupEnd();
  }
}

// 创建全局性能监控实例
export const authPerformanceMonitor = new AuthPerformanceMonitor();

// 导出便捷方法
export const markCheckAuthStart = () => authPerformanceMonitor.markCheckAuthStart();
export const markCheckAuthEnd = () => authPerformanceMonitor.markCheckAuthEnd();
export const markLanguageSyncStart = () => authPerformanceMonitor.markLanguageSyncStart();
export const markLanguageSyncEnd = () => authPerformanceMonitor.markLanguageSyncEnd();
export const markRouteLoadStart = () => authPerformanceMonitor.markRouteLoadStart();
export const markRouteLoadEnd = () => authPerformanceMonitor.markRouteLoadEnd();
export const markDeviceControllerInitStart = () => authPerformanceMonitor.markDeviceControllerInitStart();
export const markDeviceControllerInitEnd = () => authPerformanceMonitor.markDeviceControllerInitEnd();
export const generateAuthPerformanceReport = () => authPerformanceMonitor.generateReport();
